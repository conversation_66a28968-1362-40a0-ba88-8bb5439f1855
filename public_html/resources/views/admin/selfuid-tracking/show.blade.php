@extends('layouts.master')

@section('title')
    <title>Chi tiết SelfUID: {{ $selfUid }} - Admin Panel</title>
@endsection

@section('style')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endsection

@section('content')

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <div class="loading-text mt-3">
                <h5>Đang tải dữ liệu...</h5>
                <p class="text-muted">Vui lòng chờ trong giây lát</p>
            </div>
        </div>
    </div>

    <script>
        // Hiển thị loading ngay lập tức khi trang bắt đầu load
        document.addEventListener('DOMContentLoaded', function() {
            var loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
        });

        // Xử lý khi người dùng bấm back/forward (pageshow event)
        window.addEventListener('pageshow', function(event) {
            // Nếu trang được load từ cache (bfcache)
            if (event.persisted) {
                var loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }
        });
    </script>

<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Chi tiết SelfUID: {{ $selfUid }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('selfuid-tracking.index') }}">SelfUID Tracking</a></li>
                        <li class="breadcrumb-item active">{{ $selfUid }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ $stats['total_requests'] }}</h3>
                            <p>Tổng số requests</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ number_format($stats['current_diamond']) }}</h3>
                            <p>Diamond hiện tại</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-gem"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ number_format($stats['current_bean']) }}</h3>
                            <p>Bean hiện tại</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-coins"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ $stats['current_room_count'] }}</h3>
                            <p>Room Count hiện tại</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-home"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chart -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Biểu đồ thay đổi theo thời gian</h3>
                    <div class="card-tools">
                        <select id="chartDays" class="form-control form-control-sm" style="width: auto;">
                            <option value="1">24 giờ qua</option>
                            <option value="7" selected>7 ngày qua</option>
                            <option value="30">30 ngày qua</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="balanceChart" height="100"></canvas>
                </div>
            </div>

            <!-- Filter Form -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Bộ lọc lịch sử</h3>
                </div>
                <div class="card-body">
                    <form method="GET">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>User Token</label>
                                    <input type="text" name="user_token" class="form-control" 
                                           value="{{ request('user_token') }}" placeholder="User Token">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Từ ngày</label>
                                    <input type="date" name="date_from" class="form-control" 
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Đến ngày</label>
                                    <input type="date" name="date_to" class="form-control" 
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary" id="filter-btn">
                                            <i class="fas fa-search"></i> Lọc
                                        </button>
                                        <a href="{{ route('selfuid-tracking.show', $selfUid) }}" class="btn btn-secondary" id="reset-btn">
                                            <i class="fas fa-undo"></i> Reset
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- History Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Lịch sử thay đổi</h3>
                    <div class="card-tools">
                        <span class="badge badge-info">{{ $history->total() }} bản ghi</span>
                        <small class="text-muted ml-2">
                            <i class="fas fa-info-circle"></i>
                            Chỉ hiển thị khi có thay đổi số liệu
                        </small>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Thời gian</th>
                                    <th>User Token</th>
                                    <th>Diamond</th>
                                    <th>Bean</th>
                                    <th>Room Count</th>
                                    <th>Client</th>
                                    <th>IP Address</th>
                                    <th>Raw Self ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($history as $item)
                                    <tr>
                                        <td>
                                            <small>{{ $item->created_at->format('d/m/Y H:i:s') }}</small>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">{{ $item->user_token }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($item->diamond_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">
                                                {{ number_format($item->bean_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                {{ $item->room_count }}
                                            </span>
                                        </td>
                                        <td>
                                            @if($item->is_old_client ?? false)
                                                <span class="badge badge-warning" title="{{ $item->client_notes }}">
                                                    <i class="fas fa-exclamation-triangle"></i> Cũ
                                                </span>
                                            @else
                                                <span class="badge badge-success">
                                                    <i class="fas fa-check"></i> Mới
                                                </span>
                                            @endif
                                        </td>
                                        <td>{{ $item->ip_address }}</td>
                                        <td>
                                            <small class="text-muted">{{ $item->raw_self_id }}</small>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    {{ $history->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@section('script')
<script>
let balanceChart;

function loadChart(days = 7) {
    fetch(`/admin/selfuid-tracking/chart-data/{{ $selfUid }}?days=${days}`)
        .then(response => response.json())
        .then(data => {
            const ctx = document.getElementById('balanceChart').getContext('2d');

            if (balanceChart) {
                balanceChart.destroy();
            }

            balanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels,
                    datasets: [
                        {
                            label: 'Diamond',
                            data: data.diamond,
                            borderColor: 'rgb(255, 193, 7)',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            tension: 0.1
                        },
                        {
                            label: 'Bean',
                            data: data.bean,
                            borderColor: 'rgb(40, 167, 69)',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.1
                        },
                        // {
                        //     label: 'Room Count',
                        //     data: data.room_count,
                        //     borderColor: 'rgb(220, 53, 69)',
                        //     backgroundColor: 'rgba(220, 53, 69, 0.1)',
                        //     tension: 0.1,
                        //     yAxisID: 'y1'
                        // }
                    ]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Diamond / Bean'
                            }
                        },
                        // y1: {
                        //     type: 'linear',
                        //     display: true,
                        //     position: 'right',
                        //     title: {
                        //         display: true,
                        //         text: 'Room Count'
                        //     },
                        //     grid: {
                        //         drawOnChartArea: false,
                        //     },
                        // }
                    }
                }
            });
        });
}

$(document).ready(function() {
    loadChart();

    $('#chartDays').change(function() {
        loadChart($(this).val());
    });

    // Ẩn loading sau khi DOM và tất cả nội dung đã load xong
    $(window).on('load', function() {
        hideLoading();
    });

    // Xử lý khi trang được hiển thị (bao gồm cả khi back/forward)
    $(window).on('pageshow', function(event) {
        if (event.originalEvent.persisted) {
            hideLoading();
        }
    });

    // Hiển thị loading khi submit form lọc
    $('#filter-btn').on('click', function(e) {
        showLoading();
    });

    // Hiển thị loading khi click reset
    $('#reset-btn').on('click', function(e) {
        showLoading();
    });

    // Hiển thị loading khi click vào các link
    $(document).on('click', 'a[href*="selfuid-tracking"]', function(e) {
        // Chỉ hiển thị loading cho các link nội bộ, không phải link external
        if (this.hostname === window.location.hostname) {
            showLoading();
        }
    });

    // Hiển thị loading khi click pagination links
    $(document).on('click', '.pagination a', function(e) {
        showLoading();
    });

    function showLoading() {
        $('#loading-overlay').fadeIn(200);
    }

    function hideLoading() {
        $('#loading-overlay').fadeOut(300);
    }

    // Backup: Ẩn loading sau 1 giây nếu window.load không trigger
    setTimeout(function() {
        if ($('#loading-overlay').is(':visible')) {
            hideLoading();
        }
    }, 1000);
});
</script>

<style>
/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: none; /* Ẩn ban đầu */
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
}

.loading-content {
    text-align: center;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e6f0;
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

.loading-text h5 {
    color: #5a5c69;
    margin-bottom: 10px;
    font-weight: 600;
}

.loading-text p {
    color: #858796;
    margin-bottom: 0;
    font-size: 14px;
}
</style>

@endsection
